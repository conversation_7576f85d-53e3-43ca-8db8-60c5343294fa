import base64
import json
import os
import sys
import glob
import time
from pathlib import Path
from pprint import pprint
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils import get_chat_model


##### 巡检目标列表 #####
a = """
- 巡检目标：高铁车厢显示屏
    - 描述：安装在高铁车厢侧面外部，用于动态显示车次、到站信息、车厢编号等内容。
    - 参照 <高铁车厢显示屏图> 进行识别
    - 识别规则： 必须非常明显的显示出'车厢号'，才能确认是'车厢显示屏', 输出描述时必须输出'车厢号'
- 巡检目标：高铁车头雨刮器
    - 描述：安装在高铁车头的雨刮器，用于清理车头的雨刮器。
    - 参照 <高铁车头雨刮器图> 进行识别

- 巡检目标：高铁边门滑槽
    - 描述：安装于车厢侧门边缘的长条形金属槽，用于引导边门滑动。
    - 参照 <高铁边门滑槽图1>、<高铁边门滑槽图2> 进行识别

- 巡检目标：高铁受电弓
    - 描述：安装在高铁车厢顶部，不会出现在车头;它的形状为弓形，用于从接触网获取电能的装置。
    - 参照 <高铁受电弓图(升起状态)>进行识别
    - 识别规则：必须明显的看到在车顶的弓形的金属结构才能确认是'高铁受电弓'

- 巡检目标：高铁车头灯罩
    - 描述：安装在高铁车头前部，保护车头前部的灯具。
    - 参照 <高铁车头灯罩图> 进行识别

- 巡检目标：高铁重联车钩
    - 描述：安装在高铁车头前部，通常位于车头与车头之间，裸露在外的金属部件，用于连接两组列车的装置。
    - 参照 <高铁重联车钩图> 进行识别

- 巡检目标：高铁风挡
    - 描述：安装在高铁车厢之间，用于连接两节车厢的拼接，是柔性密封型外壳。
    - 参照 <高铁风挡图1>、<高铁风挡图2> 进行识别
"""

system_prompt = """
##### 角色 #####
- 你是一位资深的高铁巡检员；你现在的任务是通过视频流的连续帧来识别出<巡检目标列表>的目标；

##### 巡检目标列表 #####
- 巡检目标：高铁车厢显示屏
    - 描述：安装在高铁车厢侧面外部，用于动态显示车次、到站信息、车厢编号等内容。
    - 参照 <高铁车厢显示屏图> 进行识别
    - 识别规则： 必须非常明显的显示出'车厢号'，才能确认是'车厢显示屏', 输出描述时必须输出'车厢号'
- 巡检目标：高铁车头雨刮器
    - 描述：安装在高铁车头的雨刮器，用于清理车头的雨刮器。
    - 参照 <高铁车头雨刮器图> 进行识别

- 巡检目标：高铁边门滑槽
    - 描述：安装于车厢侧门边缘的长条形金属槽，用于引导边门滑动。
    - 参照 <高铁边门滑槽图1>、<高铁边门滑槽图2> 进行识别

- 巡检目标：高铁受电弓
    - 描述：安装在高铁车厢顶部，不会出现在车头;它的形状为弓形，用于从接触网获取电能的装置。
    - 参照 <高铁受电弓图(升起状态)>进行识别
    - 识别规则：必须明显的看到在车顶的弓形的金属结构才能确认是'高铁受电弓'

- 巡检目标：高铁车头灯罩
    - 描述：安装在高铁车头前部，保护车头前部的灯具。
    - 参照 <高铁车头灯罩图> 进行识别

- 巡检目标：高铁重联车钩
    - 描述：安装在高铁车头前部，通常位于车头与车头之间，裸露在外的金属部件，用于连接两组列车的装置。
    - 参照 <高铁重联车钩图> 进行识别

- 巡检目标：高铁风挡
    - 描述：安装在高铁车厢之间，用于连接两节车厢的拼接，是柔性密封型外壳。
    - 参照 <高铁风挡图1> 进行识别



##### 输出格式 #####
1. 如果不存在 <巡检目标列表> 中的'巡检目标'， 仅输出以下json格式: 
[]
2. 如果是清晰识别到<巡检目标列表>中的'巡检目标'，必须清晰在图片中圈出目标，仅输出以下json 格式：
[
    {
        "name": "frame_000001_t1.1s",
        "description": "目标的简短描述",
        "target": ["目标1", "目标2", ..."],
        "confidence": "置信度，取值0-1，越接近1越好; 只保留 >=0.8 为高置信度，高清晰度"
        "grounding_bbox": [[xmin, ymin, xmax, ymax], [xmin, ymin, xmax, ymax], ...]
    }
]


##### 巡检工作流程 #####
1. 分析用户给出的每一帧的内容，记住其特征及名称；
2. 逐一比对每一帧的内容，判断是否为<巡检目标列表>中的'巡检目标'；
3. 如果<巡检目标列表>中的'巡检目标'存在'识别规则'，则必须遵循'识别规则'来确认'巡检目标'；
4. 由于给出的是连续帧，如果在单一帧中无法看清目标，可以结合多帧来判断。如果目标不清晰，直接跳过该图片即可；
5. 放弃识别远处且模糊的在<巡检目标列表>中的'巡检目标'，识别近处清晰的'巡检目标'即可；
6. 找出目标后，严格遵循<输出格式>进行内容输出，直到每一帧都判断完毕；
"""

image_display_screen_prompt = """##### 高铁车厢显示屏图 ######"""
image_wiper_prompt = """##### 高铁车头雨刮器图 ######"""
image_sliding_slot_1_prompt = """##### 高铁边门滑槽图1 ######"""
image_sliding_slot_2_prompt = """##### 高铁边门滑槽图2 ######"""
image_pantograph_raised_prompt = """##### 高铁受电弓图(升起状态) ######"""
image_pantograph_lowered_prompt = """##### 高铁受电弓图(降落状态) ######"""
image_headlight_shield_prompt = """##### 高铁车头灯罩图 ######"""
image_coupler_prompt = """##### 高铁重联车钩图 ######"""
image_wind_shield_1_prompt = """##### 高铁风挡图1 ######"""
image_wind_shield_2_prompt = """##### 高铁风挡图2 ######"""

user_start_prompt = """开始巡检任务， 请根据 <巡检工作流程> 开始你的巡检"""

display_screen_image = "test/analyze/mark_inspection_object_img/display_screen_marked.jpg"
wiper_image = "test/analyze/mark_inspection_object_img/wiper_marked.jpg"
sliding_slot_image_1 = "test/analyze/mark_inspection_object_img/door_slide_channel_2_marked.jpg"
sliding_slot_image_2 = "test/analyze/mark_inspection_object_img/door_slide_channel_3_marked.jpg"
pantograph_raised_image = "test/analyze/mark_inspection_object_img/pantograph_raised_marked.jpg"
pantograph_lowered_image = "test/analyze/mark_inspection_object_img/pantograph_lowered_marked.jpg"
headlight_shield_image_1 = "test/analyze/mark_inspection_object_img/train_light_2_marked.jpg"
headlight_shield_image_2 = "test/analyze/mark_inspection_object_img/train_light_3_marked.jpg"
coupler_image = "test/analyze/mark_inspection_object_img/coupler_marked.jpg"
wind_shield_image_1 = "test/analyze/mark_inspection_object_img/windshield_1_marked.jpg"
wind_shield_image_2 = "test/analyze/mark_inspection_object_img/windshield_2_marked.jpg"


def encode_image_to_base64(image_path: str) -> str:
    """将图片编码为base64格式"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def get_frame_images(frames_dir: str) -> List[str]:
    """获取指定目录下的所有frame图片文件，按文件名排序"""
    if not os.path.exists(frames_dir):
        raise FileNotFoundError(f"目录不存在: {frames_dir}")

    # 获取所有jpg图片文件
    pattern = os.path.join(frames_dir, "frame_*.jpg")
    image_files = glob.glob(pattern)

    # 按文件名排序
    image_files.sort()

    print(f"找到 {len(image_files)} 张图片")
    return image_files


def extract_frame_id(image_path: str) -> str:
    """从图片路径中提取frame ID，如 frame_00103"""
    filename = os.path.basename(image_path)
    # 提取frame_XXXXXX部分
    if filename.startswith("frame_"):
        frame_id = filename.split("_")[0] + "_" + filename.split("_")[1] + "_" + filename.split("_")[2]
        return frame_id
    return filename


def create_batch_message(image_paths: List[str]) -> dict:
    """为一批图片创建vision模型消息，每张图片都有对应的文本说明"""
    # 构建消息内容，先添加总体提示

    content = [
        {
            "type": "text",
            "text": "好，现在开始执行你的<巡检任务>"
        },
        {
            "type": "text",
            "text": f"我将发送{len(image_paths)}张图片，每张图片前都会标注其名称："
        }
    ]

    # 为每张图片添加：文本说明 + 图片
    for image_path in image_paths:
        frame_id = extract_frame_id(image_path)

        # 添加图片名称说明
        content.append({
            "type": "text",
            "text": f"以下图片名称为{frame_id}："
        })

        # 添加对应的图片
        try:
            base64_image = encode_image_to_base64(image_path)
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                }
            })
        except Exception as e:
            print(f"警告：无法编码图片 {image_path}: {str(e)}")
            # 如果图片编码失败，添加错误说明
            content.append({
                "type": "text",
                "text": f"（图片{frame_id}编码失败）"
            })

    return {"role": "user", "content": content}


def analyze_inspection_object(
        frames_dir: str = "assets/extracted_frames_20250904_170940",
        batch_size: int = 30,
        timeout_seconds: int = 300
):
    """
    通过图片分析高铁巡检目标物体

    Args:
        frames_dir: 图片目录路径
        batch_size: 每批发送的图片数量，默认30张
        timeout_seconds: 每次API调用的超时时间（秒），默认300秒（5分钟）
    """
    print("🚄 开始高铁巡检目标物体分析")
    print("=" * 60)

    # 记录总体开始时间
    total_start_time = time.time()
    results = []

    try:
        # 获取vision模型
        vision_model = get_chat_model(model_name="vision")
        print(f"✅ Vision模型加载成功: {type(vision_model).__name__}")

        # 获取所有图片文件
        image_files = get_frame_images(frames_dir)

        if not image_files:
            print("❌ 未找到任何图片文件")
            return results

        # 按批次处理图片
        total_batches = (len(image_files) + batch_size - 1) // batch_size
        print(f"📊 将分 {total_batches} 批处理，每批 {batch_size} 张图片")
        print("-" * 60)

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(image_files))
            batch_images = image_files[start_idx:end_idx]

            print(f"\n🔍 处理第 {batch_idx + 1}/{total_batches} 批")
            print(f"📷 图片范围: {extract_frame_id(batch_images[0])} 到 {extract_frame_id(batch_images[-1])}")
            print(f"📊 图片数量: {len(batch_images)}")

            # 记录批次开始时间
            batch_start_time = time.time()

            # 重试机制：最多重试3次
            max_retries = 1
            retry_count = 0
            batch_success = False

            while retry_count < max_retries and not batch_success:
                try:
                    if retry_count > 0:
                        print(f"🔄 第 {retry_count + 1} 次重试...")

                    # 记录单次尝试开始时间
                    attempt_start_time = time.time()

                    # system message
                    system_message = {
                        "role": "system",
                        "content": system_prompt
                    }

                    # reference images message
                    reference_image_messages = {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": image_display_screen_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(display_screen_image)}"
                                }
                            },
                            {
                                "type": "text",
                                "text": image_wiper_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(wiper_image)}"
                                }
                            },
                            {
                                "type": "text",
                                "text": image_sliding_slot_1_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(sliding_slot_image_1)}"
                                }
                            },
                            {
                                "type": "text",
                                "text": image_sliding_slot_2_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(sliding_slot_image_2)}"
                                }
                            },
                            {
                                "type": "text",
                                "text": image_pantograph_raised_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(pantograph_raised_image)}"
                                }
                            },
                            # {
                            #     "type": "text",
                            #     "text": image_pantograph_lowered_prompt
                            # },
                            # {
                            #     "type": "image_url",
                            #     "image_url": {
                            #         "url": f"data:image/jpeg;base64,{encode_image_to_base64(pantograph_lowered_image)}"
                            #     }
                            # },
                            {
                                "type": "text",
                                "text": image_headlight_shield_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(headlight_shield_image_1)}"
                                }
                            },
                            {
                                "type": "text",
                                "text": image_coupler_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(coupler_image)}"
                                }
                            },
                            {
                                "type": "text",
                                "text": image_wind_shield_1_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(wind_shield_image_1)}"
                                }
                            },
                            {
                                "type": "text",
                                "text": image_wind_shield_2_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(wind_shield_image_2)}"
                                }
                            },

                        ]
                    }

                    # 创建待分析图片的消息
                    batch_message = create_batch_message(batch_images)

                    # 发送给vision模型
                    print("🤖 正在分析...")
                    analysis_start_time = time.time()

                    # 使用传入的超时时间参数

                    try:
                        # 使用timeout配置调用vision模型
                        response = vision_model.invoke(
                            [system_message, reference_image_messages, batch_message],
                            config={"timeout": timeout_seconds}
                        )
                        analysis_end_time = time.time()
                        analysis_duration = analysis_end_time - analysis_start_time
                    except Exception as timeout_error:
                        analysis_end_time = time.time()
                        analysis_duration = analysis_end_time - analysis_start_time
                        if "timeout" in str(timeout_error).lower():
                            print(f"⏰ 分析超时 ({timeout_seconds}秒)，耗时: {analysis_duration:.2f}秒")
                        else:
                            print(f"❌ 分析出错: {str(timeout_error)}")
                        raise timeout_error

                    print(f"✅ 分析完成! (耗时: {analysis_duration:.2f}秒)")
                    print("\n📝 分析结果:")
                    print("=" * 60)
                    print(response.content)
                    print("=" * 60)

                    # 解析JSON结果
                    try:
                        batch_results = json.loads(response.content)
                        if isinstance(batch_results, list):
                            results.extend(batch_results)
                            batch_success = True
                            # 计算批次总耗时
                            batch_end_time = time.time()
                            batch_duration = batch_end_time - batch_start_time
                            print(f"✅ 第 {batch_idx + 1} 批JSON解析成功 (批次总耗时: {batch_duration:.2f}秒)")
                        else:
                            print(f"⚠️ 响应格式不是列表: {type(batch_results)}")
                            retry_count += 1
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON解析失败: {str(e)}")
                        print(f"原始响应: {response.content}")
                        retry_count += 1
                        if retry_count < max_retries:
                            print(f"🔄 将进行第 {retry_count + 1} 次重试...")

                except Exception as e:
                    print(f"❌ 第 {batch_idx + 1} 批处理失败: {str(e)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"🔄 将进行第 {retry_count + 1} 次重试...")

            # 如果重试3次都失败，跳过当前批次
            if not batch_success:
                batch_end_time = time.time()
                batch_duration = batch_end_time - batch_start_time
                print(f"❌ 第 {batch_idx + 1} 批重试 {max_retries} 次后仍然失败，跳过此批次 (耗时: {batch_duration:.2f}秒)")
                continue

        # 计算总体耗时
        total_end_time = time.time()
        total_duration = total_end_time - total_start_time

        print(f"\n🎉 高铁巡检目标物体分析完成!")
        print(f"📊 总共识别到 {len(results)} 个目标")
        print(f"⏱️ 总耗时: {total_duration:.2f}秒 ({total_duration/60:.1f}分钟)")
        print(f"📈 平均每批耗时: {total_duration/total_batches:.2f}秒")

    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")

    finally:
        # 将结果序列化到JSON文件
        output_file = "test/analyze/inspection_object.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已保存到: {output_file}")
            print(f"📄 文件包含 {len(results)} 条识别记录")
        except Exception as e:
            print(f"❌ 保存结果文件失败: {str(e)}")

        return results


if __name__ == "__main__":
    # 运行巡检分析
    data = analyze_inspection_object(
        # frames_dir="assets/extracted_frames_20250907_172156",
        frames_dir="assets/extracted_frames_20250904_170940",
        batch_size=20
    )
    pprint(data)