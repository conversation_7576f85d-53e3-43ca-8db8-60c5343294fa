#!/usr/bin/env python3
"""
测试脚本：演示带有耗时统计的巡检分析功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from test.analyze.train_inspection_object import analyze_inspection_object

def test_timing_analysis():
    """测试带有耗时统计的分析功能"""
    print("🧪 开始测试带有耗时统计的巡检分析功能")
    print("=" * 60)
    
    # 使用较小的批次大小进行测试
    results = analyze_inspection_object(
        frames_dir="assets/extracted_frames_20250904_170940",
        batch_size=5  # 使用小批次进行测试
    )
    
    print(f"\n✅ 测试完成，共识别到 {len(results)} 个目标")
    return results

if __name__ == "__main__":
    test_timing_analysis()
