[app]
name = "HighSpeedTrain"
host = "0.0.0.0"
log_level = "DEBUG"
thread_pool_size = 100
http_port = 8888
http_openapi_url = "/openapi.json"
http_docs_url = "/docs"

[models]
seed_thinking = "seed_thinking"
fix = "gpt5"
vision = "vision"
seed = "seed"
ui_tars = "ui_tars"


[paths]
# 截图相关路径
screenshot_base_dir = "reports/ui_agent_test/reports/"

# 视频相关路径
video_base_dir = "reports/ui_agent_test/video"
video_temp_dir = "reports/ui_agent_test//video/temp"

# 视频服务配置
video_max_upload_size = 52428800    # 50MB
video_session_timeout = 600         # 10分钟
video_hmac_secret = "video_upload_secret_key_2025"

# 豆包AI配置
doubao_api_key = "506a6605-1c08-4694-ab53-d559a761cfde"
doubao_base_url = "https://ark.cn-beijing.volces.com/api/v3/"
doubao_model = "doubao-seed-1.6-250615"

[llms.gpt4o]
provider = "azure_openai"
temperature = 0.4
model = "gpt-4o"
external_args = { azure_endpoint = "https://east-us2-quwan-yw-infra-02.openai.azure.com", azure_deployment = "gpt-4o", api_version = "2025-04-01-preview", api_key = "${AZURE_OPENAI_API_KEY}" }

[llms.gpt41]
provider = "azure_openai"
temperature = 0.4
model = "gpt-4.1"
external_args = { azure_endpoint = "https://east-us2-quwan-yw-infra-02.openai.azure.com", azure_deployment = "gpt-4.1", api_version = "2025-04-01-preview", api_key = "${AZURE_OPENAI_API_KEY}", max_tokens = 8192, timeout = 30, max_retries = 3 }

[llms.gpt5]
provider = "azure_openai"
temperature = 0.4
model = "gpt-5-chat"
external_args = { azure_endpoint = "https://east-us2-quwan-yw-infra-02.openai.azure.com", azure_deployment = "gpt-5-chat", api_version = "2025-04-01-preview", api_key = "${AZURE_OPENAI_API_KEY}", max_completion_tokens = 8192, timeout = 30, max_retries = 3 }

[llms.ui_tars]
provider = "openai"
temperature = 0.2
model = "doubao-1-5-ui-tars-250428"
external_args = { base_url = "https://ark.cn-beijing.volces.com/api/v3/", api_key = "506a6605-1c08-4694-ab53-d559a761cfde" }

[llms.vision]
provider = "openai"
temperature = 0.2
model = "doubao-seed-1-6-vision-250815"
external_args = { base_url = "https://ark.cn-beijing.volces.com/api/v3/", api_key = "506a6605-1c08-4694-ab53-d559a761cfde",max_tokens = 32768 }
extra_body = { "thinking" = { "type" = "disabled" } }

[llms.seed_thinking]
provider = "openai"
temperature = 0
model = "doubao-seed-1-6-thinking-250715"
external_args = { base_url = "https://ark.cn-beijing.volces.com/api/v3/", api_key = "506a6605-1c08-4694-ab53-d559a761cfde" }



[llms.seed]
provider = "openai"
temperature = 0
model = "doubao-seed-1-6-250615"
external_args = { base_url = "https://ark.cn-beijing.volces.com/api/v3/", api_key = "506a6605-1c08-4694-ab53-d559a761cfde" }
extra_body = { "thinking" = { "type" = "disabled" } }


